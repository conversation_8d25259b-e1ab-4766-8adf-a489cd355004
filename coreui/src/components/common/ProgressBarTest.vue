<template>
  <div class="progress-bar-test">
    <CCard>
      <CCardHeader>
        <CIcon name="cil-speedometer" />
        <strong>CoreUI ProgressBar Test</strong>
      </CCardHeader>
      <CCardBody>
        <CRow>
          <CCol md="12" class="mb-3">
            <h5>Test the migrated ProgressBar component</h5>
            <p class="text-muted">
              This component has been migrated from Vuetify to CoreUI components and native HTML.
              All functionality should work exactly as before.
            </p>
          </CCol>
        </CRow>

        <CRow>
          <CCol md="6" class="mb-3">
            <CButton 
              color="primary" 
              @click="testBasicLoading"
              block
            >
              <CIcon name="cil-reload" class="mr-1" />
              Test Basic Loading
            </CButton>
          </CCol>

          <CCol md="6" class="mb-3">
            <CButton 
              color="success" 
              @click="testProgressBar"
              block
            >
              <CIcon name="cil-chart-line" class="mr-1" />
              Test Progress Bar
            </CButton>
          </CCol>

          <CCol md="6" class="mb-3">
            <CButton 
              color="warning" 
              @click="testCancellable"
              block
            >
              <CIcon name="cil-x" class="mr-1" />
              Test Cancellable
            </CButton>
          </CCol>

          <CCol md="6" class="mb-3">
            <CButton 
              color="info" 
              @click="testDifferentColors"
              block
            >
              <CIcon name="cil-color-palette" class="mr-1" />
              Test Different Colors
            </CButton>
          </CCol>
        </CRow>

        <hr class="my-4">

        <CRow>
          <CCol md="12">
            <h6>Migration Status</h6>
            <CBadge color="success" class="mr-2">✅ Vuetify Dependencies Removed</CBadge>
            <CBadge color="success" class="mr-2">✅ CoreUI Components Integrated</CBadge>
            <CBadge color="success" class="mr-2">✅ Native Modal Implementation</CBadge>
            <CBadge color="success" class="mr-2">✅ All Features Preserved</CBadge>
            <CBadge color="success" class="mr-2">✅ Backward Compatibility</CBadge>
          </CCol>
        </CRow>

        <CRow class="mt-3">
          <CCol md="12">
            <h6>Technical Details</h6>
            <ul class="list-unstyled">
              <li><strong>Modal Implementation:</strong> Native HTML with CSS animations</li>
              <li><strong>Button Component:</strong> CoreUI CButton</li>
              <li><strong>Icons:</strong> CoreUI CIcon (cil-reload, cil-x)</li>
              <li><strong>Styling:</strong> SCSS with CoreUI design system</li>
              <li><strong>Animations:</strong> CSS keyframes (fadeIn, slideIn, spin)</li>
              <li><strong>Responsive:</strong> Mobile-friendly with media queries</li>
              <li><strong>Dark Theme:</strong> Supports .c-dark-theme class</li>
            </ul>
          </CCol>
        </CRow>
      </CCardBody>
    </CCard>

    <!-- Progress Bar Component Reference -->
    <ProgressBar ref="progressBar" />
  </div>
</template>

<script>
import ProgressBar from './ProgressBar.vue';

export default {
  name: 'ProgressBarTest',
  
  components: {
    ProgressBar
  },
  
  methods: {
    /**
     * Test basic loading functionality
     */
    testBasicLoading() {
      this.$refs.progressBar.open({
        title: 'Testing Basic Loading',
        subtitle: 'CoreUI Implementation',
        message: 'This modal is now using native HTML and CoreUI components...',
        color: 'primary'
      });

      // Auto close after 3 seconds
      setTimeout(() => {
        this.$refs.progressBar.close();
      }, 3000);
    },

    /**
     * Test progress bar with percentage
     */
    testProgressBar() {
      this.$refs.progressBar.open({
        title: 'Testing Progress Bar',
        subtitle: 'With percentage display',
        showPercentage: true,
        progress: 0,
        color: 'success',
        message: 'Starting process...'
      });

      // Simulate progress updates
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          this.$refs.progressBar.updateProgress(progress, 'Process completed!');
          setTimeout(() => {
            this.$refs.progressBar.close();
          }, 1000);
          clearInterval(interval);
        } else {
          this.$refs.progressBar.updateProgress(
            progress, 
            `Processing... ${Math.round(progress)}% complete`
          );
        }
      }, 600);
    },

    /**
     * Test cancellable operation
     */
    testCancellable() {
      this.$refs.progressBar.open({
        title: 'Testing Cancellable Operation',
        subtitle: 'Click backdrop or cancel button to stop',
        message: 'This operation can be cancelled by the user...',
        showCancel: true,
        color: 'warning'
      }).then((result) => {
        if (result) {
          console.log('Operation completed successfully');
          this.$toast?.success?.('Operation completed!');
        } else {
          console.log('Operation was cancelled');
          this.$toast?.info?.('Operation cancelled by user');
        }
      });

      // Auto complete after 10 seconds if not cancelled
      setTimeout(() => {
        if (this.$refs.progressBar.dialog) {
          this.$refs.progressBar.agree();
        }
      }, 10000);
    },

    /**
     * Test different color themes
     */
    testDifferentColors() {
      const colors = ['primary', 'success', 'warning', 'danger', 'info'];
      let currentIndex = 0;

      const showNext = () => {
        if (currentIndex < colors.length) {
          const color = colors[currentIndex];
          this.$refs.progressBar.open({
            title: `Testing ${color.charAt(0).toUpperCase() + color.slice(1)} Theme`,
            subtitle: `Color theme: ${color}`,
            message: `This is the ${color} color theme with gradient effects...`,
            color: color,
            theme: 'gradient'
          });

          setTimeout(() => {
            this.$refs.progressBar.close();
            currentIndex++;
            if (currentIndex < colors.length) {
              setTimeout(showNext, 500);
            }
          }, 2000);
        }
      };

      showNext();
    }
  }
};
</script>

<style lang="scss" scoped>
.progress-bar-test {
  .mr-1 {
    margin-right: 0.25rem;
  }
  
  .mr-2 {
    margin-right: 0.5rem;
  }
  
  .my-4 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .mt-3 {
    margin-top: 1rem;
  }
  
  .mb-3 {
    margin-bottom: 1rem;
  }
  
  .list-unstyled {
    padding-left: 0;
    list-style: none;
    
    li {
      margin-bottom: 0.5rem;
      
      strong {
        color: #495057;
      }
    }
  }
}
</style>
