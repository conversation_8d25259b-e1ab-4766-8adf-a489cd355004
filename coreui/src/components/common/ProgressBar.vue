<template>
  <!-- Native Modal Implementation with CoreUI styling -->
  <div
    v-if="dialog"
    class="modal-backdrop enhanced-progress-backdrop"
    :style="{ zIndex: options.zIndex }"
    @click="handleBackdropClick"
  >
    <div
      class="modal-dialog enhanced-progress-modal"
      :style="{ maxWidth: options.width + 'px' }"
      @click.stop
    >
      <div class="modal-content enhanced-progress-card">
        <!-- Modern Header with Gradient -->
        <div class="progress-header">
          <div class="header-content">
            <div class="loading-icon-wrapper">
              <CIcon
                name="cil-reload"
                class="loading-icon"
                :class="{ 'spinning': !options.hideSpinner }"
              />
            </div>
            <div class="header-text">
              <h6 class="loading-title">{{ options.title || 'Processing' }}</h6>
              <p class="loading-subtitle" v-if="options.subtitle">{{ options.subtitle }}</p>
            </div>
          </div>
        </div>

        <!-- Enhanced Progress Section -->
        <div class="modal-body progress-content">
          <!-- Main Progress Bar -->
          <div class="progress-container">
            <div class="progress-wrapper">
              <div class="progress-track">
                <div
                  class="progress-fill"
                  :class="progressBarClass"
                  :style="progressStyle"
                ></div>
                <div class="progress-glow" :style="progressStyle"></div>
              </div>
            </div>

            <!-- Progress Percentage -->
            <div class="progress-info" v-if="options.showPercentage && options.progress !== undefined">
              <span class="progress-percentage">{{ Math.round(options.progress || 0) }}%</span>
            </div>
          </div>

          <!-- Status Message -->
          <div class="status-message" v-if="options.message">
            <p class="message-text">{{ options.message }}</p>
          </div>

          <!-- Animated Dots for Indeterminate Progress -->
          <div class="loading-dots" v-if="!options.showPercentage">
            <div class="dot" v-for="n in 3" :key="n"></div>
          </div>
        </div>

        <!-- Action Buttons (if needed) -->
        <div class="modal-footer progress-actions" v-if="options.showCancel">
          <CButton
            color="secondary"
            variant="outline"
            @click="cancel"
            class="cancel-btn"
          >
            <CIcon name="cil-x" class="mr-1" />
            Cancel
          </CButton>
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import { mapState, mapActions } from "vuex";
import { CIcon } from '../../services/icons-vue/src';

export default {
  name: 'EnhancedProgressBar',

  components: {
    CIcon
  },

  data: () => ({
    dialog: false,
    resolve: null,
    reject: null,
    options: {
      color: "primary",
      width: 400,
      zIndex: 1000000000,
      title: "Loading",
      subtitle: "",
      message: "",
      progress: undefined,
      showPercentage: false,
      showCancel: false,
      hideSpinner: false,
      theme: "gradient"
    },
  }),

  computed: {
    ...mapState("app", ["progressBar"]),

    /**
     * Progress bar CSS class based on theme and color
     */
    progressBarClass() {
      const baseClass = 'progress-fill';
      const colorClass = `progress-${this.options.color || 'primary'}`;
      const themeClass = this.options.theme === 'gradient' ? 'gradient-theme' : 'solid-theme';

      return [baseClass, colorClass, themeClass];
    },

    /**
     * Progress bar style for width animation
     */
    progressStyle() {
      if (this.options.progress !== undefined) {
        const progress = Math.min(100, Math.max(0, this.options.progress || 0));
        return {
          width: `${progress}%`,
          transition: 'width 0.3s ease-in-out'
        };
      }
      return {
        width: '100%',
        animation: 'indeterminateProgress 2s ease-in-out infinite'
      };
    }
  },

  methods: {
    ...mapActions("app", ["loadVApp","unLoadVApp"]),
    /**
     * Open progress dialog with options
     */
    open(options = {}) {
      this.loadVApp();
      this.dialog = true;

      // Merge options with defaults
      this.options = {
        ...this.options,
        ...options,
        width: options.width || 400
      };

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },

    /**
     * Close dialog with success
     */
    agree() {
      if (this.resolve) {
        this.resolve(true);
      }
      this.close();
    },

    /**
     * Close dialog with cancellation
     */
    cancel() {
      if (this.resolve) {
        this.resolve(false);
      }
      this.close();
    },

    /**
     * Close dialog and cleanup
     */
    close() {
      this.dialog = false;
      this.unLoadVApp();

      // Reset options to defaults after a delay
      setTimeout(() => {
        this.options = {
          color: "primary",
          width: 400,
          zIndex: 1000000000,
          title: "Loading",
          subtitle: "",
          message: "",
          progress: undefined,
          showPercentage: false,
          showCancel: false,
          hideSpinner: false,
          theme: "gradient"
        };
      }, 300);
    },

    /**
     * Update progress value
     */
    updateProgress(progress, message = null) {
      this.options.progress = progress;
      if (message) {
        this.options.message = message;
      }
    },

    /**
     * Handle backdrop click (only allow close if cancellable)
     */
    handleBackdropClick() {
      if (this.options.showCancel) {
        this.cancel();
      }
      // Otherwise, do nothing (persistent modal)
    }
  },

  watch: {
    progressBar(newVal, oldVal) {
      this.dialog = newVal;
      if (newVal) {
        this.loadVApp();
        return;
      }
      this.unLoadVApp();
    }
  },
};
</script>

<style lang="scss" scoped>
/* Enhanced Progress Bar Styles - Native Modal Implementation */

/* Modal Backdrop */
.enhanced-progress-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

/* Modal Dialog */
.enhanced-progress-modal {
  position: relative;
  margin: 1.75rem auto;
  max-width: 500px;
  width: calc(100% - 2rem);
  animation: slideIn 0.3s ease-out;
}

/* Modal Content */
.enhanced-progress-card {
  border-radius: 16px !important;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
              0 10px 20px rgba(0, 0, 0, 0.1) !important;
  border: none;
  background: white;

  .modal-body {
    padding: 32px 24px !important;
  }
}

/* Modern Header with Gradient */
.progress-header {
  background: linear-gradient(135deg, #005cc8 0%, #4189de 100%);
  padding: 20px 24px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    pointer-events: none;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;
    position: relative;
    z-index: 1;
  }

  .loading-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .loading-icon {
    color: white;
    font-size: 24px;
    transition: transform 0.3s ease;

    &.spinning {
      animation: spin 2s linear infinite;
    }
  }

  .header-text {
    flex: 1;

    .loading-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
      margin: 0;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .loading-subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      margin: 4px 0 0 0;
      opacity: 0.9;
    }
  }
}

/* Progress Content */
.progress-content {
  padding: 32px 24px !important;
  background: linear-gradient(180deg, #fafbfc 0%, #ffffff 100%);
}

/* Progress Container */
.progress-container {
  margin-bottom: 20px;

  .progress-wrapper {
    position: relative;
    margin-bottom: 12px;
  }

  .progress-track {
    position: relative;
    height: 8px;
    background: #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  }

  .progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    border-radius: 12px;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &.gradient-theme {
      &.progress-primary {
        background: linear-gradient(90deg, #005cc8 0%, #4189de 100%);
        box-shadow: 0 2px 8px rgba(0, 92, 200, 0.3);
      }

      &.progress-success {
        background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
      }

      &.progress-warning {
        background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
      }

      &.progress-danger {
        background: linear-gradient(90deg, #dc3545 0%, #e74c3c 100%);
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
      }

      &.progress-info {
        background: linear-gradient(90deg, #17a2b8 0%, #6f42c1 100%);
        box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
      }
    }

    &.solid-theme {
      &.progress-primary { background: #005cc8; }
      &.progress-success { background: #28a745; }
      &.progress-warning { background: #ffc107; }
      &.progress-danger { background: #dc3545; }
      &.progress-info { background: #17a2b8; }
    }
  }

  .progress-glow {
    position: absolute;
    top: -2px;
    left: 0;
    height: calc(100% + 4px);
    border-radius: 12px;
    opacity: 0.6;
    filter: blur(4px);
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
  }

  .progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .progress-percentage {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      background: #f3f4f6;
      padding: 4px 12px;
      border-radius: 20px;
      border: 1px solid #e5e7eb;
    }
  }
}

/* Status Message */
.status-message {
  text-align: center;
  margin-bottom: 16px;

  .message-text {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
  }
}

/* Animated Loading Dots */
.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 8px;

  .dot {
    width: 8px;
    height: 8px;
    background: #005cc8;
    border-radius: 50%;
    animation: dotPulse 1.4s ease-in-out infinite both;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

/* Progress Actions - Modal Footer */
.progress-actions {
  padding: 16px 24px !important;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;

  .cancel-btn {
    border-radius: 8px !important;
    text-transform: none !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    padding: 8px 16px !important;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .mr-1 {
      margin-right: 4px;
    }
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes indeterminateProgress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Dark Theme Support */
.c-dark-theme {
  .enhanced-progress-backdrop {
    background: rgba(0, 0, 0, 0.7);
  }

  .enhanced-progress-card {
    background: #2d3748 !important;

    .modal-body {
      background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%) !important;
    }

    .progress-track {
      background: #4a5568;
    }

    .progress-info .progress-percentage {
      background: #4a5568;
      color: #e2e8f0;
      border-color: #718096;
    }

    .status-message .message-text {
      color: #a0aec0;
    }

    .progress-actions {
      background: #1a202c;
      border-color: #4a5568;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-progress-modal {
    margin: 1rem auto;
    width: calc(100% - 1rem);
  }

  .enhanced-progress-card {
    .progress-header {
      padding: 16px 20px;

      .header-content {
        gap: 12px;
      }

      .loading-icon-wrapper {
        width: 40px;
        height: 40px;
      }

      .loading-icon {
        font-size: 20px;
      }

      .loading-title {
        font-size: 16px;
      }

      .loading-subtitle {
        font-size: 13px;
      }
    }

    .modal-body {
      padding: 24px 20px !important;
    }

    .progress-actions {
      padding: 12px 20px !important;
    }
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .loading-icon.spinning,
  .dot,
  .progress-fill,
  .progress-glow {
    animation: none !important;
  }

  .cancel-btn:hover {
    transform: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .progress-track {
    border: 2px solid #000;
  }

  .progress-fill {
    border: 1px solid #000;
  }

  .loading-title,
  .loading-subtitle {
    text-shadow: none;
  }
}
</style>
